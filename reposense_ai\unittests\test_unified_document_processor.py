"""
Unit tests for the UnifiedDocumentProcessor class.

This module tests the unified document processing functionality including:
- Task queuing and processing
- Historical scan processing
- File system processing
- Content processing
- Date parsing and metadata extraction
- Error handling and edge cases
"""

import json
import tempfile
import threading
import time
from datetime import datetime
from pathlib import Path
from queue import Queue
from unittest.mock import <PERSON><PERSON>ock, Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config, temp_dir

# Import the module under test
try:
    from document_database import DocumentRecord
    from models import CommitInfo, RepositoryConfig
    from unified_document_processor import (
        ProcessingSource,
        ProcessingTask,
        UnifiedDocumentProcessor,
    )

    IMPORTS_AVAILABLE = True
except ImportError:
    # Set placeholder variables for missing imports
    DocumentRecord = None
    CommitInfo = None
    RepositoryConfig = None
    ProcessingSource = None
    ProcessingTask = None
    UnifiedDocumentProcessor = None
    IMPORTS_AVAILABLE = False


@pytest.mark.unit
@pytest.mark.processor
class TestUnifiedDocumentProcessor:
    """Test cases for UnifiedDocumentProcessor class."""

    def test_processor_import(self):
        """Test that UnifiedDocumentProcessor can be imported successfully."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        assert UnifiedDocumentProcessor is not None, (
            "UnifiedDocumentProcessor should be importable"
        )
        assert ProcessingTask is not None, "ProcessingTask should be importable"
        assert ProcessingSource is not None, "ProcessingSource should be importable"

    def test_processor_initialization(self, temp_dir):
        """Test UnifiedDocumentProcessor initialization."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        output_dir = temp_dir / "output"
        db_path = temp_dir / "test.db"

        processor = UnifiedDocumentProcessor(
            output_dir=str(output_dir), db_path=str(db_path), max_concurrent_tasks=2
        )

        assert processor is not None
        assert processor.output_dir == output_dir
        assert processor.max_concurrent_tasks == 2
        assert processor.running is False
        assert hasattr(processor, "task_queue")
        assert hasattr(processor, "stats")

    def test_processing_task_creation(self):
        """Test ProcessingTask creation and priority ordering."""
        if ProcessingTask is None or ProcessingSource is None:
            pytest.skip("ProcessingTask or ProcessingSource not available")

        # Test basic task creation
        task1 = ProcessingTask(
            source=ProcessingSource.HISTORICAL_SCAN,
            priority=5,
            filepath="/test/file.md",
        )

        task2 = ProcessingTask(
            source=ProcessingSource.WEB_INTERFACE, priority=10, content="test content"
        )

        assert task1.source == ProcessingSource.HISTORICAL_SCAN
        assert task1.priority == 5
        assert task1.filepath == "/test/file.md"

        assert task2.source == ProcessingSource.WEB_INTERFACE
        assert task2.priority == 10
        assert task2.content == "test content"

        # Test priority ordering (higher priority should be "less than" for max-heap)
        assert task2 < task1  # task2 has higher priority (10 > 5)

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_start_stop_processor(self, mock_metadata_extractor, mock_db, temp_dir):
        """Test starting and stopping the processor."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"),
            db_path=str(temp_dir / "test.db"),
            max_concurrent_tasks=2,
        )

        # Test start
        processor.start()
        assert processor.running is True
        assert len(processor.processing_threads) == 2

        # Give threads a moment to start
        time.sleep(0.1)

        # Test stop
        processor.stop()
        assert processor.running is False
        assert len(processor.processing_threads) == 0

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_process_commit(self, mock_metadata_extractor, mock_db, temp_dir):
        """Test processing a commit from historical scanning."""
        if (
            UnifiedDocumentProcessor is None
            or CommitInfo is None
            or RepositoryConfig is None
        ):
            pytest.skip("Required classes not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Create test commit info
        commit_info = CommitInfo(
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="123",
            author="Test Author",
            date="2025-08-24T10:00:00Z",
            message="Test commit message",
            changed_paths=["file1.py", "file2.py"],
            diff="@@ -1,3 +1,4 @@\n def main():\n+    print('Test')\n     pass",
        )

        # Create test repository config
        repo_config = RepositoryConfig(
            name="test-repo",
            url="https://example.com/repo",
            type="svn",
            risk_aggressiveness="MEDIUM",
        )

        # Test queuing commit
        result = processor.process_commit(
            commit_info=commit_info,
            repository_config=repo_config,
            documentation="Test documentation content",
            priority=5,
        )

        assert result is True
        assert processor.task_queue.qsize() == 1

        # Get the queued task
        task = processor.task_queue.get_nowait()
        assert task.source == ProcessingSource.HISTORICAL_SCAN
        assert task.priority == 5
        assert task.commit_info == commit_info
        assert task.repository_config == repo_config
        assert task.content == "Test documentation content"

    def test_date_parsing_with_z_suffix(self, temp_dir):
        """Test date parsing with Z suffix (the specific issue mentioned)."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Create a test document file with date field
        test_content = """
# Test Document

**Author:** Test Author
**Date:** 2025-08-24T10:30:45Z
**Message:** Test commit message

Some document content here.
        """

        doc_file = temp_dir / "test_doc.md"
        doc_file.write_text(test_content, encoding="utf-8")

        # Test the date parsing logic directly
        with patch.object(
            processor.metadata_extractor, "extract_field"
        ) as mock_extract:
            mock_extract.side_effect = lambda content, field: {
                "Author": "Test Author",
                "Date": "2025-08-24T10:30:45Z",
                "Message": "Test commit message",
            }.get(field)

            # This should not raise an exception
            date_field = "2025-08-24T10:30:45Z"
            try:
                doc_date = datetime.fromisoformat(date_field.replace("Z", "+00:00"))
                assert doc_date.year == 2025
                assert doc_date.month == 8
                assert doc_date.day == 24
                assert doc_date.hour == 10
                assert doc_date.minute == 30
                assert doc_date.second == 45
            except ValueError as e:
                pytest.fail(f"Date parsing failed: {e}")

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_get_stats(self, mock_metadata_extractor, mock_db, temp_dir):
        """Test getting processor statistics."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test initial stats
        stats = processor.get_stats()
        assert isinstance(stats, dict)
        assert "processed_count" in stats
        assert "error_count" in stats
        assert "processing_time_total" in stats
        assert "queue_size" in stats
        assert "active_threads" in stats
        assert "running" in stats

        assert stats["processed_count"] == 0
        assert stats["error_count"] == 0
        assert stats["running"] is False

    def test_convert_to_bool(self, temp_dir):
        """Test boolean conversion utility method."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test None
        assert processor._convert_to_bool(None) is None

        # Test boolean values
        assert processor._convert_to_bool(True) is True
        assert processor._convert_to_bool(False) is False

        # Test string values
        assert processor._convert_to_bool("true") is True
        assert processor._convert_to_bool("TRUE") is True
        assert processor._convert_to_bool("yes") is True
        assert processor._convert_to_bool("1") is True
        assert processor._convert_to_bool("on") is True
        assert processor._convert_to_bool("enabled") is True

        assert processor._convert_to_bool("false") is False
        assert processor._convert_to_bool("FALSE") is False
        assert processor._convert_to_bool("no") is False
        assert processor._convert_to_bool("0") is False
        assert processor._convert_to_bool("off") is False
        assert processor._convert_to_bool("disabled") is False

        # Test non-empty string (should be True)
        assert processor._convert_to_bool("some text") is True
        assert processor._convert_to_bool("") is False

        # Test dictionary values
        assert processor._convert_to_bool({"level": "high"}) is True
        assert processor._convert_to_bool({"level": "yes"}) is True
        assert processor._convert_to_bool({"level": "low"}) is False

        # Test other types
        assert processor._convert_to_bool(1) is True
        assert processor._convert_to_bool(0) is False

    def test_convert_to_string(self, temp_dir):
        """Test string conversion utility method."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test None
        assert processor._convert_to_string(None) is None

        # Test string values
        assert processor._convert_to_string("test") == "test"

        # Test dictionary values with priority keys
        assert processor._convert_to_string({"priority": "HIGH"}) == "HIGH"
        assert processor._convert_to_string({"level": "MEDIUM"}) == "MEDIUM"
        assert processor._convert_to_string({"risk_level": "LOW"}) == "LOW"

        # Test dictionary values with priority keys (returns value directly)
        assert processor._convert_to_string({"assessment": "critical"}) == "critical"
        assert processor._convert_to_string({"priority": "HIGH"}) == "HIGH"
        assert processor._convert_to_string({"level": "MEDIUM"}) == "MEDIUM"

        # Test dictionary values with high/medium indicators (no priority keys)
        assert processor._convert_to_string({"status": "critical"}) == "HIGH"
        assert processor._convert_to_string({"status": "moderate"}) == "MEDIUM"

        # Test dictionary fallback to first non-empty value
        assert processor._convert_to_string({"other": "value"}) == "value"

        # Test other types
        assert processor._convert_to_string(123) == "123"
        assert processor._convert_to_string(True) == "True"

    def test_parse_document_id_from_filename(self, temp_dir):
        """Test parsing document ID from filename."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test revision_ pattern
        repo_id, revision = processor._parse_document_id_from_filename(
            "revision_123.md"
        )
        assert repo_id is None  # Should be determined from path
        assert revision == 123

        # Test repo_name_revision pattern
        repo_id, revision = processor._parse_document_id_from_filename(
            "test_repo_456.md"
        )
        assert repo_id == "test_repo"
        assert revision == 456

        # Test complex repo name
        repo_id, revision = processor._parse_document_id_from_filename(
            "my_complex_repo_name_789.md"
        )
        assert repo_id == "my_complex_repo_name"
        assert revision == 789

        # Test invalid patterns
        repo_id, revision = processor._parse_document_id_from_filename(
            "invalid_file.md"
        )
        assert repo_id is None
        assert revision is None

        repo_id, revision = processor._parse_document_id_from_filename(
            "revision_abc.md"
        )
        assert repo_id is None
        assert revision is None

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_process_file(self, mock_metadata_extractor, mock_db, temp_dir):
        """Test processing a file from the file system."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        test_file = temp_dir / "test_document.md"
        test_file.write_text("Test content", encoding="utf-8")

        # Test queuing file
        result = processor.process_file(str(test_file), priority=3)
        assert result is True
        assert processor.task_queue.qsize() == 1

        # Get the queued task
        task = processor.task_queue.get_nowait()
        assert task.source == ProcessingSource.FILE_SYSTEM
        assert task.priority == 3
        assert task.filepath == str(test_file)

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_process_content(self, mock_metadata_extractor, mock_db, temp_dir):
        """Test processing content directly."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Create test document record
        doc_record = DocumentRecord(
            id="test-doc-001",
            repository_id="test-repo",
            repository_name="Test Repository",
            revision=1,
            date=datetime.now(),
            filename="test.md",
            filepath="/test/test.md",
            size=100,
            author="Test Author",
            commit_message="Test message",
            risk_level="LOW",
        )

        # Test queuing content
        result = processor.process_content(
            content="Test content for processing",
            document_record=doc_record,
            priority=8,
        )

        assert result is True
        assert processor.task_queue.qsize() == 1

        # Get the queued task
        task = processor.task_queue.get_nowait()
        assert task.source == ProcessingSource.WEB_INTERFACE
        assert task.priority == 8
        assert task.content == "Test content for processing"
        assert task.document_record == doc_record

    def test_metadata_extraction_timeout_calculation(self, temp_dir):
        """Test metadata extraction timeout calculation based on model size."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        # Mock config manager
        mock_config_manager = Mock()
        mock_config = Mock()
        mock_config.ollama_timeout_base = 180
        mock_config.ollama_model_risk_assessment = "granite3.3:8b"
        mock_config.ollama_model_code_review = "qwen3-coder:latest"
        mock_config.ollama_model_documentation = "smollm2:latest"
        mock_config.ollama_model = "smollm2:latest"
        mock_config_manager.load_config.return_value = mock_config

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"),
            db_path=str(temp_dir / "test.db"),
            config_manager=mock_config_manager,
        )

        # Test with medium-large model (8b)
        timeout = processor._get_metadata_extraction_timeout()
        assert timeout >= 270  # At least 4.5 minutes for 8b model

        # Test with large model
        mock_config.ollama_model_risk_assessment = "llama2:70b"
        timeout = processor._get_metadata_extraction_timeout()
        assert timeout >= 360  # At least 6 minutes for 70b model

        # Test with small model (default)
        mock_config.ollama_model_risk_assessment = "smollm2:latest"
        mock_config.ollama_model_code_review = "smollm2:latest"
        timeout = processor._get_metadata_extraction_timeout()
        assert timeout >= 180  # At least 3 minutes for small models

    def test_needs_processing_logic(self, temp_dir):
        """Test file processing need determination logic."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        # Mock database
        mock_db = Mock()

        with patch("unified_document_processor.DocumentDatabase", return_value=mock_db):
            processor = UnifiedDocumentProcessor(
                output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
            )

            test_filepath = "/test/file.md"
            current_time = time.time()

            # Test force rescan
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=True
                )
                is True
            )

            # Test no existing document
            mock_db.get_documents_by_filepath.return_value = []
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=False
                )
                is True
            )

            # Test existing document never processed
            mock_doc = Mock()
            mock_doc.processed_time = None
            mock_db.get_documents_by_filepath.return_value = [mock_doc]
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=False
                )
                is True
            )

            # Test existing document with no file modification time
            mock_doc.processed_time = datetime.now()
            mock_doc.file_modified_time = None
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=False
                )
                is True
            )

            # Test file modified after processing
            mock_doc.file_modified_time = (
                current_time - 100
            )  # File modified 100 seconds ago
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=False
                )
                is True
            )

            # Test file not modified since processing
            mock_doc.file_modified_time = (
                current_time + 100
            )  # File "modified" in future (processed after modification)
            assert (
                processor._needs_processing(
                    test_filepath, current_time, force_rescan=False
                )
                is False
            )

    def test_error_handling_in_processing_loop(self, temp_dir):
        """Test error handling in the processing loop."""
        if (
            UnifiedDocumentProcessor is None
            or ProcessingTask is None
            or ProcessingSource is None
        ):
            pytest.skip("Required classes not available")

        with (
            patch("unified_document_processor.DocumentDatabase"),
            patch("unified_document_processor.MetadataExtractor"),
        ):
            processor = UnifiedDocumentProcessor(
                output_dir=str(temp_dir / "output"),
                db_path=str(temp_dir / "test.db"),
                max_concurrent_tasks=1,
            )

            # Mock _process_task to raise an exception
            with patch.object(
                processor, "_process_task", side_effect=Exception("Test error")
            ):
                # Create a test task
                task = ProcessingTask(
                    source=ProcessingSource.FILE_SYSTEM,
                    priority=1,
                    filepath="/test/file.md",
                )

                processor.task_queue.put(task)

                # Start processor briefly
                processor.start()
                time.sleep(0.1)  # Let it process the task
                processor.stop()

                # Check that error was counted
                stats = processor.get_stats()
                assert stats["error_count"] > 0


@pytest.mark.integration
@pytest.mark.processor
class TestUnifiedDocumentProcessorIntegration:
    """Integration tests for UnifiedDocumentProcessor with realistic scenarios."""

    def test_file_system_scan(self, temp_dir):
        """Test scanning file system for documents."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        # Create test directory structure
        output_dir = temp_dir / "output"
        repos_dir = output_dir / "repositories"
        test_repo_dir = repos_dir / "test_repo" / "docs"
        test_repo_dir.mkdir(parents=True, exist_ok=True)

        # Create test markdown files
        (test_repo_dir / "revision_1.md").write_text("Test content 1", encoding="utf-8")
        (test_repo_dir / "revision_2.md").write_text("Test content 2", encoding="utf-8")
        (test_repo_dir / "not_a_revision.txt").write_text(
            "Not a markdown file", encoding="utf-8"
        )

        with (
            patch("unified_document_processor.DocumentDatabase"),
            patch("unified_document_processor.MetadataExtractor"),
        ):
            processor = UnifiedDocumentProcessor(
                output_dir=str(output_dir), db_path=str(temp_dir / "test.db")
            )

            # Mock _needs_processing to return True for all files
            with patch.object(processor, "_needs_processing", return_value=True):
                queued_count = processor.scan_file_system(force_rescan=False)

                # Should find 2 markdown files
                assert queued_count == 2
                assert processor.task_queue.qsize() == 2

    def test_complex_date_parsing_scenarios(self, temp_dir):
        """Test various date parsing scenarios that could occur in real documents."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test various date formats that might appear in documents
        test_dates = [
            "2025-08-24T10:30:45Z",  # ISO with Z
            "2025-08-24T10:30:45+00:00",  # ISO with timezone
            "2025-08-24T10:30:45.123Z",  # ISO with milliseconds and Z
            "2025-08-24T10:30:45.123456Z",  # ISO with microseconds and Z
        ]

        for date_str in test_dates:
            try:
                # This is the actual parsing logic from the code
                parsed_date = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
                assert parsed_date.year == 2025
                assert parsed_date.month == 8
                assert parsed_date.day == 24
                assert parsed_date.hour == 10
                assert parsed_date.minute == 30
                assert parsed_date.second == 45
            except ValueError as e:
                pytest.fail(f"Failed to parse date '{date_str}': {e}")

    @patch("unified_document_processor.DocumentDatabase")
    @patch("unified_document_processor.MetadataExtractor")
    def test_process_commit_with_model_overrides(
        self, mock_metadata_extractor, mock_db, temp_dir
    ):
        """Test processing a commit with model and aggressiveness overrides."""
        if (
            UnifiedDocumentProcessor is None
            or CommitInfo is None
            or RepositoryConfig is None
            or ProcessingSource is None
        ):
            pytest.skip("Required classes not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Create test commit info
        commit_info = CommitInfo(
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="456",
            author="Test Author",
            date="2025-08-24T10:00:00Z",
            message="Test commit with overrides",
            changed_paths=["file1.py"],
            diff="@@ -1,2 +1,3 @@\n def test():\n+    print('Override test')\n     pass",
        )

        # Create test repository config
        repo_config = RepositoryConfig(
            name="test-repo",
            url="https://example.com/repo",
            type="svn",
            risk_aggressiveness="LOW",
        )

        # Test queuing commit with overrides
        result = processor.process_commit_with_model(
            commit_info=commit_info,
            repository_config=repo_config,
            documentation="Test documentation with overrides",
            priority=7,
            override_model="custom-model:latest",
            override_aggressiveness="HIGH",
        )

        assert result is True
        assert processor.task_queue.qsize() == 1

        # Get the queued task and verify overrides
        task = processor.task_queue.get_nowait()
        assert task.source == ProcessingSource.HISTORICAL_SCAN
        assert task.priority == 7
        assert task.override_model == "custom-model:latest"
        assert task.override_aggressiveness == "HIGH"

    def test_find_repository_config(self, temp_dir):
        """Test finding repository configuration by ID or name."""
        if not IMPORTS_AVAILABLE:
            pytest.skip("Required imports not available")

        # Import required classes
        try:
            from database_migration import DatabaseMigration
            from models import RepositoryConfig
            from repository_database import RepositoryDatabase
        except ImportError:
            pytest.skip("Required classes not available")

        db_path = str(temp_dir / "test.db")

        # Initialize database with proper schema
        migration = DatabaseMigration(db_path)
        assert migration.initialize_database(), "Failed to initialize database"

        # Create repository database and add test repositories
        repo_db = RepositoryDatabase(db_path)

        # Create test repository configs
        repo1 = RepositoryConfig(
            name="test-repo-1",
            url="https://example.com/test-repo-1",
            type="git",
            id="12345678-1234-1234-1234-123456789001",
        )

        repo2 = RepositoryConfig(
            name="test-repo-2",
            url="https://example.com/test-repo-2",
            type="git",
            id="12345678-1234-1234-1234-123456789002",
        )

        repo3 = RepositoryConfig(
            name="complex_repo_name",
            url="https://example.com/complex_repo_name",
            type="svn",
        )

        # Insert repositories into database
        assert repo_db.create_repository(repo1)
        assert repo_db.create_repository(repo2)
        assert repo_db.create_repository(repo3)

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"),
            db_path=db_path,
        )

        # Test finding by exact name
        found_repo = processor._find_repository_config("test-repo-1")
        assert found_repo is not None
        assert found_repo.name == "test-repo-1"
        assert found_repo.id == "12345678-1234-1234-1234-123456789001"

        # Test finding by ID
        found_repo = processor._find_repository_config(
            "12345678-1234-1234-1234-123456789002"
        )
        assert found_repo is not None
        assert found_repo.name == "test-repo-2"
        assert found_repo.id == "12345678-1234-1234-1234-123456789002"

        # Test finding by base name when repo_id contains branch path
        found_repo = processor._find_repository_config("complex_repo_name/trunk")
        assert found_repo is not None
        assert found_repo.name == "complex_repo_name"

        # Test not found
        found_repo = processor._find_repository_config("nonexistent-repo")
        assert found_repo is None

        # Test with None input
        found_repo = processor._find_repository_config(None)
        assert found_repo is None

    def test_cache_invalidation_signal_creation(self, temp_dir):
        """Test creation of cache invalidation signal."""
        if UnifiedDocumentProcessor is None:
            pytest.skip("UnifiedDocumentProcessor not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Mock the pathlib.Path import inside the method
        cache_signal_path = temp_dir / "cache_invalidate_signal"

        with patch("pathlib.Path") as mock_path:
            mock_path_instance = Mock()
            mock_path.return_value = mock_path_instance

            # Test signal creation
            processor._create_cache_invalidation_signal()

            # Verify Path was called with correct argument and touch was called
            mock_path.assert_called_once_with("/app/data/cache_invalidate_signal")
            mock_path_instance.touch.assert_called_once()

    def test_error_handling_with_invalid_dates(self, temp_dir):
        """Test error handling when parsing invalid dates."""
        if UnifiedDocumentProcessor is None:
            pytest.skip("UnifiedDocumentProcessor not available")

        processor = UnifiedDocumentProcessor(
            output_dir=str(temp_dir / "output"), db_path=str(temp_dir / "test.db")
        )

        # Test invalid date formats that should not crash the system
        invalid_dates = [
            "invalid-date-format",
            "2025-13-45T25:70:90Z",  # Invalid month, day, hour, minute, second
            "",  # Empty string
            None,  # None value
        ]

        for invalid_date in invalid_dates:
            try:
                if invalid_date:
                    # This should handle the error gracefully
                    datetime.fromisoformat(invalid_date.replace("Z", "+00:00"))
                else:
                    # None or empty should be handled before reaching fromisoformat
                    pass
            except (ValueError, AttributeError):
                # These exceptions are expected for invalid dates
                pass  # This is the expected behavior

    def test_processing_source_enum(self):
        """Test ProcessingSource enum values."""
        if ProcessingSource is None:
            pytest.skip("ProcessingSource not available")

        # Test all enum values exist and have correct string values
        assert ProcessingSource.HISTORICAL_SCAN.value == "historical_scan"
        assert ProcessingSource.FILE_SYSTEM.value == "file_system"
        assert ProcessingSource.WEB_INTERFACE.value == "web_interface"
        assert ProcessingSource.API_REQUEST.value == "api_request"

        # Test enum can be used in comparisons
        assert ProcessingSource.HISTORICAL_SCAN != ProcessingSource.FILE_SYSTEM
        assert ProcessingSource.WEB_INTERFACE == ProcessingSource.WEB_INTERFACE


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
